#!/usr/bin/env node

import { request } from 'https';
import { writeFileSync } from 'fs';
import { join } from 'path';

const GRAPHQL_ENDPOINT = 'https://2xymbka4zzam3b347bgugpfp6a.appsync-api.us-east-2.amazonaws.com/graphql';
const API_KEY = 'da2-hu56cxlaxreutk6qxxkxhab7ly';

const introspectionQuery = `
  query IntrospectionQuery {
    __schema {
      queryType { name }
      mutationType { name }
      subscriptionType { name }
      types {
        ...FullType
      }
      directives {
        name
        description
        locations
        args {
          ...InputValue
        }
      }
    }
  }

  fragment FullType on __Type {
    kind
    name
    description
    fields(includeDeprecated: true) {
      name
      description
      args {
        ...InputValue
      }
      type {
        ...TypeRef
      }
      isDeprecated
      deprecationReason
    }
    inputFields {
      ...InputValue
    }
    interfaces {
      ...TypeRef
    }
    enumValues(includeDeprecated: true) {
      name
      description
      isDeprecated
      deprecationReason
    }
    possibleTypes {
      ...TypeRef
    }
  }

  fragment InputValue on __InputValue {
    name
    description
    type { ...TypeRef }
    defaultValue
  }

  fragment TypeRef on __Type {
    kind
    name
    ofType {
      kind
      name
      ofType {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                }
              }
            }
          }
        }
      }
    }
  }
`;

function fetchSchema() {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify({
      query: introspectionQuery
    });

    const options = {
      hostname: '2xymbka4zzam3b347bgugpfp6a.appsync-api.us-east-2.amazonaws.com',
      port: 443,
      path: '/graphql',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    const req = request(options, (res) => {
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          if (result.errors) {
            reject(new Error(`GraphQL errors: ${JSON.stringify(result.errors)}`));
          } else {
            resolve(result);
          }
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

async function updateSchema() {
  try {
    console.log('Fetching GraphQL schema...');
    const result = await fetchSchema();
    
    const schemaPath = join(__dirname, '..', 'schema.json');
    writeFileSync(schemaPath, JSON.stringify(result, null, 2));
    
    console.log('✅ Schema updated successfully!');
    console.log(`📁 Schema saved to: ${schemaPath}`);
    
    // Now you can run the Amplify codegen if needed
    console.log('\n💡 You can now run: npx @aws-amplify/cli codegen');
    
  } catch (error) {
    console.error('❌ Failed to update schema:', error.message);
    process.exit(1);
  }
}

updateSchema();
