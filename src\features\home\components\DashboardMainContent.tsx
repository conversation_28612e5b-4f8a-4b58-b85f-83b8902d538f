import { Card } from "@/components/ui/card";
import { Album, Briefcase, PlayCircle, Send, User } from "lucide-react";
import { useRouter } from "next/navigation";

export default function DashboardMainContent() {
  const router = useRouter();
  return (
    <section className="flex-1 px-8 py-8">
      {/* My Active Projects */}
      <div className="flex items-center justify-between mb-6">
        {/* <h2 className="text-2xl font-bold">My Active Projects</h2> */}
        {/* <a href="#" className="text-sm text-primary hover:underline">Show all</a> */}
      </div>
      <div className="grid md:grid-cols-3 gap-6 mb-10">
        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/playlist')}>
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
            <PlayCircle />
          </div>
          <h3 className="text-lg font-semibold mb-2">Playlist</h3>
          <p className="text-muted-foreground">Create and enjoy personalized playlists curated for your taste.</p>
        </Card>
        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/artist')}>
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
            <User />
          </div>
          <h3 className="text-lg font-semibold mb-2">Artist</h3>
          <p className="text-muted-foreground">Discover top artists, explore their profiles, and follow your favorites.</p>
        </Card>
        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/album')}>
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
            <Album />
          </div>
          <h3 className="text-lg font-semibold mb-2">Album</h3>
          <p className="text-muted-foreground">Browse and listen to curated albums across various genres.</p>
        </Card>
        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/opportunities')}>
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
            <Briefcase />
          </div>
          <h3 className="text-lg font-semibold mb-2">Opportunities</h3>
          <p className="text-muted-foreground">Explore the latest career openings and project opportunities.</p>
        </Card>
        <Card className="p-6 hover:shadow-lg transition-shadow cursor-pointer" onClick={() => router.push('/applications')}>
          <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4 mx-auto">
            <Send />
          </div>
          <h3 className="text-lg font-semibold mb-2">My Applications</h3>
          <p className="text-muted-foreground">Track the status and progress of your submitted job applications.</p>
        </Card>
      </div>
      {/* My Tasks */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">My Tasks</h2>
        <a href="#" className="text-sm text-primary hover:underline">Show all</a>
      </div>
      <div className="grid md:grid-cols-2 gap-4 mb-10">
        {/* Example tasks (replace with real data) */}
        {[1,2].map((_,i) => (
          <Card key={i} className="p-4 flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <div>
                <div className="font-semibold">Task Name</div>
                <div className="text-xs text-muted-foreground">Mix Ready for your review</div>
              </div>
              <button className="bg-primary text-white rounded px-4 py-1 text-xs font-semibold">Review</button>
            </div>
            <div className="text-xs text-muted-foreground">Sarah Anderson · 2 hours ago</div>
          </Card>
        ))}
      </div>
      {/* Recommended Gigs */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-bold">Recommended Gigs</h2>
        <a href="#" className="text-sm text-primary hover:underline">Show all</a>
      </div>
      <div className="grid md:grid-cols-2 gap-4">
        {/* Example gigs (replace with real data) */}
        {[1,2].map((_,i) => (
          <Card key={i} className="p-4 flex flex-col gap-2">
            <div className="font-semibold">Gig Name</div>
            <div className="text-xs text-muted-foreground">A short description of the gig opportunity.</div>
          </Card>
        ))}
      </div>
    </section>
  );
}
