/* tslint:disable */
/* eslint-disable */
//  This file was automatically generated and should not be edited.

export type Artist = {
  __typename: "Artist",
  id: string,
  name: string,
  bio?: string | null,
  formedDate?: string | null,
  disbandedDate?: string | null,
  // Nullable
  location?: string | null,
};

export type GetAllArtistQueryVariables = {
};

export type GetAllArtistQuery = {
  // Gets a list of all artists.
  getAllArtist:  Array< {
    __typename: "Artist",
    id: string,
    name: string,
    bio?: string | null,
    formedDate?: string | null,
    disbandedDate?: string | null,
    // Nullable
    location?: string | null,
  } >,
};

export type SearchArtistByNameQueryVariables = {
  name: string,
};

export type SearchArtistByNameQuery = {
  // Searches for artists by name (partial, case-insensitive match).
  searchArtistByName:  Array< {
    __typename: "Artist",
    id: string,
    name: string,
    bio?: string | null,
    formedDate?: string | null,
    disbandedDate?: string | null,
    // Nullable
    location?: string | null,
  } >,
};
