/* tslint:disable */
/* eslint-disable */
//  This file was automatically generated and should not be edited.

export type Artist = {
  __typename: "Artist",
  id: string,
  name: string,
  bio?: string | null,
  formedDate?: string | null,
  disbandedDate?: string | null,
  // Nullable
  location?: string | null,
};

export type ArtistProfile = {
  __typename: "ArtistProfile",
  artist?: Artist | null,
  songs?:  Array<Song | null > | null,
  albums?:  Array<Album | null > | null,
};

export type Song = {
  __typename: "Song",
  id: string,
  title?: string | null,
  duration?: number | null,
  recordID?: string | null,
  releaseDate?: string | null,
  coverPhoto?: string | null,
  role?: string | null,
  credits?:  Array<Credit | null > | null,
};

export type Credit = {
  __typename: "Credit",
  artistId?: string | null,
  role?: string | null,
};

export type Album = {
  __typename: "Album",
  id: string,
  releaseDate?: string | null,
  genre?: Array< string | null > | null,
  description?: string | null,
  coverArtURL?: string | null,
  title?: string | null,
};

export type GetAllArtistQueryVariables = {
};

export type GetAllArtistQuery = {
  // Gets a list of all artists.
  getAllArtist:  Array< {
    __typename: "Artist",
    id: string,
    name: string,
    bio?: string | null,
    formedDate?: string | null,
    disbandedDate?: string | null,
    // Nullable
    location?: string | null,
  } >,
};

export type SearchArtistByNameQueryVariables = {
  name: string,
};

export type SearchArtistByNameQuery = {
  // Searches for artists by name (partial, case-insensitive match).
  searchArtistByName:  Array< {
    __typename: "Artist",
    id: string,
    name: string,
    bio?: string | null,
    formedDate?: string | null,
    disbandedDate?: string | null,
    // Nullable
    location?: string | null,
  } >,
};

export type GetArtistProfileQueryVariables = {
  artistId: string,
};

export type GetArtistProfileQuery = {
  getArtistProfile?:  {
    __typename: "ArtistProfile",
    artist?:  {
      __typename: "Artist",
      id: string,
      name: string,
      bio?: string | null,
      formedDate?: string | null,
      disbandedDate?: string | null,
      // Nullable
      location?: string | null,
    } | null,
    songs?:  Array< {
      __typename: "Song",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
      role?: string | null,
    } | null > | null,
    albums?:  Array< {
      __typename: "Album",
      id: string,
      releaseDate?: string | null,
      genre?: Array< string | null > | null,
      description?: string | null,
      coverArtURL?: string | null,
      title?: string | null,
    } | null > | null,
  } | null,
};
