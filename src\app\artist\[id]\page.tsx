import { ArtistDetailsPage } from "@/features/artist/components";
import { artistsData } from "@/features/artist/utils/constants";


// Generate static params for static export
export async function generateStaticParams() {
  // Return a list of possible IDs for static generation
  // In a real app, you would fetch this from your API or database
  return artistsData.map((artist)=> {return {id : artist.id}});
}

export default function ArtistDetails() {
  return <ArtistDetailsPage />;
}
