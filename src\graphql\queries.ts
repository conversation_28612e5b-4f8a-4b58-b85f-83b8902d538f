/* tslint:disable */
// this is an auto generated file. This will be overwritten

import * as APITypes from "../API";
type GeneratedQuery<InputType, OutputType> = string & {
  __generatedQueryInput: InputType;
  __generatedQueryOutput: OutputType;
};

export const getAllArtist = /* GraphQL */ `query GetAllArtist {
  getAllArtist {
    id
    name
    bio
    formedDate
    disbandedDate
    location
    __typename
  }
}
` as GeneratedQuery<
  APITypes.GetAllArtistQueryVariables,
  APITypes.GetAllArtistQuery
>;
export const searchArtistByName = /* GraphQL */ `query SearchArtistByName($name: String!) {
  searchArtistByName(name: $name) {
    id
    name
    bio
    formedDate
    disbandedDate
    location
    __typename
  }
}
` as GeneratedQuery<
  APITypes.SearchArtistByNameQueryVariables,
  APITypes.SearchArtistByNameQuery
>;
