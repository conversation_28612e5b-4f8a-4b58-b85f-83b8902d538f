"use client";

import { But<PERSON> } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import {  <PERSON><PERSON><PERSON>, Bell, Settings, LogIn, LogOut, User } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";
import { useAuth as useAuthContext } from "@/contexts/auth/AuthContext";
import Image from "next/image";
import ThemeToggle from "@/components/shared/theme/ThemeToggle";
import LanguageToggle from "@/components/shared/language/LanguageToggle";
import { isAuthOnlyRoute, isOnboarding } from "@/lib/auth-routes";

export default function Header() {
  const router = useRouter();
  const { isAuthenticated, isLoading, user, signOut } = useAuthContext();

  const handleSignOut = async () => {
    await signOut();
  };

    const pathname = usePathname();
  

   if(isAuthOnlyRoute(pathname) || isOnboarding(pathname)){
      return null;
    }

  return (
    <header className="sticky top-0 z-50 flex justify-between items-center p-6 bg-background border-b backdrop-blur-sm bg-background/95">
      {/* Left: Logo */}
      <div className="flex items-center gap-2 cursor-pointer" onClick={() => router.push("/")}>
        <Image src="/music-icon.svg" width={30} height={30} alt="logo" />
        <h2 className="text-2xl font-bold">Smash Music</h2>
      </div>
      {/* Center: Search Bar */}
      <div className="flex-1 flex justify-center">
        <div className="flex items-center w-full max-w-xl bg-muted rounded-xl px-4 py-2 border border-muted-foreground/10">
          <Sparkles className="w-5 h-5 text-muted-foreground mr-2" />
          <input
            type="text"
            placeholder="Search music, projects, collaborators or ask a question"
            className="flex-1 bg-transparent outline-none text-base placeholder:text-muted-foreground"
          />
        </div>
      </div>
      {/* Right: Premium, Bell, Settings, Avatar Dropdown */}
      <div className="flex items-center gap-4 ml-4">
        <Button variant="outline" className="rounded-full px-6 py-2 font-medium">
          Explore Premium
        </Button>
        <Bell className="w-6 h-6 cursor-pointer text-muted-foreground" />
        {/* Settings Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-9 h-9 rounded-full flex items-center justify-center focus:outline-none">
              <Settings className="w-6 h-6 text-muted-foreground" />
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="min-w-[200px]">
            <div className="px-2 py-2 flex gap-2 justify-center items-center">
              <LanguageToggle />
              <ThemeToggle />
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <button className="w-9 h-9 rounded-full bg-muted flex items-center justify-center focus:outline-none">
              {/* Avatar or initials */}
              {isAuthenticated && user && (user.email || user.username) ? (
                <span className="w-7 h-7 rounded-full bg-primary/90 text-white flex items-center justify-center font-semibold uppercase select-none">
                  {((user.email || user.username)?.[0] || '').toUpperCase()}
                </span>
              ) : (
                <span className="w-7 h-7 rounded-full bg-muted-foreground/30 flex items-center justify-center text-muted-foreground font-semibold select-none">
                  <User className="w-4 h-4" />
                </span>
              )}
            </button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="min-w-[180px]">
            {!isLoading && (
              isAuthenticated ? (
                <>
                  <DropdownMenuItem className="pointer-events-none select-none text-muted-foreground">
                    Welcome, {user?.email || user?.username}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleSignOut} className="gap-2 cursor-pointer">
                    <LogOut className="w-4 h-4" /> Sign Out
                  </DropdownMenuItem>
                </>
              ) : (
                <>
                  <DropdownMenuItem onClick={() => router.push('/login')} className="gap-2 cursor-pointer">
                    <LogIn className="w-4 h-4" /> Sign In
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => router.push('/signup')} className="gap-2 cursor-pointer">
                    <User className="w-4 h-4" /> Sign Up
                  </DropdownMenuItem>
                </>
              )
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
