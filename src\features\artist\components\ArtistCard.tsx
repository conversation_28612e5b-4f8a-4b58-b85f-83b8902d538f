import Image from "next/image"
import Link from "next/link"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { MapPin, Calendar, ExternalLink } from "lucide-react"
import { Artist } from "../types/artist"


interface ArtistCardProps {
  artist: Artist
}

export function ArtistCard({ artist }: ArtistCardProps) {
  const getActiveYears = () => {
    const startYear = new Date(artist.formedDate).getFullYear()
    if (artist.disbandedDate) {
      const endYear = new Date(artist.disbandedDate).getFullYear()
      return `${startYear} - ${endYear}`
    }
    return `${startYear} - Present`
  }

  const isActive = !artist.disbandedDate

  return (
    <Card className="group hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center space-y-4">
          {/* Artist Image */}
          <div className="relative w-24 h-24 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center">
            <Image
              src="/dummy-image.png?height=96&width=96"
              alt={artist.name}
              width={96}
              height={96}
              className="rounded-full object-cover"
            />
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-full" />
          </div>

          {/* Artist Name */}
          <div className="space-y-2">
            <h3 className="text-xl font-bold group-hover:text-primary transition-colors">{artist.name}</h3>
            <div className="flex items-center justify-center gap-2">
              {isActive ? (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Active
                </Badge>
              ) : (
                <Badge variant="outline" className="text-muted-foreground">
                  Disbanded
                </Badge>
              )}
            </div>
          </div>

          {/* Bio */}
          <p className="text-muted-foreground text-sm line-clamp-2 min-h-[2.5rem]">{artist.bio}</p>

          {/* Location */}
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <MapPin className="w-4 h-4" />
            <span>{artist.location}</span>
          </div>

          {/* Active Since */}
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Calendar className="w-4 h-4" />
            <span>Active: {getActiveYears()}</span>
          </div>

          {/* View Profile Button */}
          <Link href={`/artist/${artist.id}`} className="w-full">
            <Button className="w-full gap-2 group-hover:bg-primary group-hover:text-primary-foreground transition-colors">
              <ExternalLink className="w-4 h-4" />
              View Profile
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
